import os
import subprocess
import logging
import platform

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# === CONFIGURATION (MODIFIEZ ICI) ===
DATASET_ID = "011"
CONFIGURATION = "2d"      # '2d', '3d_fullres', '3d_lowres'
PLANS_NAME = "nnUNetPlans"
EPOCHS = 5                # Doit correspondre au modèle entraîné

# === CONFIGURATION DES FOLDS ===
# Folds à exporter - peut inclure des nombres ET/OU "all"
# Exemples:
#   ["all"] = export seulement fold_all
#   [0, 1, 2, 3, 4] = export folds individuels 0-4
#   [0, 1, 2, 3, 4, "all"] = export folds 0-4 + fold_all
#   [2, 3, "all"] = export folds 2, 3 + fold_all
VALIDATION_FOLDS = ["all"]

# === DÉTECTION SYSTÈME ET PATHS ===
# Détection automatique du système d'exploitation
IS_WINDOWS = platform.system() == "Windows"

# Configuration des chemins selon le système
if IS_WINDOWS:
    # Chemins Windows
    RAW_PATH = r"C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_raw"
    PREPROCESSED_PATH = r"C:\Users\<USER>\Documents\datasets\nnUnet\nnUnet_preprocessed"
    RESULTS_PATH = r"C:\Users\<USER>\Documents\results\nnUnet_results"
else:
    # Chemins Linux/Unix
    RAW_PATH = "/mnt/datasets/nnUnet/nnUnet_raw"
    PREPROCESSED_PATH = "/mnt/datasets/nnUnet/nnUnet_preprocessed"
    RESULTS_PATH = "/mnt/results/nnUnet_results"

# === CONFIGURATION ENVIRONNEMENT ===
os.environ["nnUNet_raw"] = RAW_PATH
os.environ["nnUNet_preprocessed"] = PREPROCESSED_PATH
os.environ["nnUNet_results"] = RESULTS_PATH

# === RECHERCHE DU DATASET ===
def find_dataset_folder():
    """Trouve automatiquement le dossier du dataset"""
    dataset_pattern = f"Dataset{DATASET_ID}_"
    for item in os.listdir(RESULTS_PATH):
        if item.startswith(dataset_pattern):
            return item
    raise FileNotFoundError(f"Dataset {DATASET_ID} non trouvé dans {RESULTS_PATH}")

print("\n[INFO] Recherche du dataset...")
dataset_name = find_dataset_folder()
logger.info(f"[DATASET] Trouvé: {dataset_name}")

# === VÉRIFICATION DES MODÈLES DISPONIBLES ===
trainer_class = f"nnUNetTrainer_{EPOCHS}epochs"
trainer_path = os.path.join(RESULTS_PATH, dataset_name, f"{trainer_class}__{PLANS_NAME}__{CONFIGURATION}")

def check_model_availability():
    """Vérifie que les modèles requis sont disponibles avant l'export"""
    if not os.path.exists(trainer_path):
        raise FileNotFoundError(
            f"[ERROR] Dossier du trainer non trouvé: {trainer_path}\n"
            f"Vérifiez que l'entraînement a été effectué avec les bons paramètres:\n"
            f"  - DATASET_ID: {DATASET_ID}\n"
            f"  - EPOCHS: {EPOCHS}\n"
            f"  - CONFIGURATION: {CONFIGURATION}"
        )

    # Vérifier les folds demandés (peut inclure des nombres et "all")
    missing_folds = []
    for fold in VALIDATION_FOLDS:
        fold_path = os.path.join(trainer_path, f"fold_{fold}")
        checkpoint_path = os.path.join(fold_path, "checkpoint_final.pth")
        if not os.path.exists(checkpoint_path):
            missing_folds.append(fold)

    if missing_folds:
        available_folds = []
        for item in os.listdir(trainer_path):
            if item.startswith("fold_") and os.path.isdir(os.path.join(trainer_path, item)):
                checkpoint = os.path.join(trainer_path, item, "checkpoint_final.pth")
                if os.path.exists(checkpoint):
                    available_folds.append(item.replace("fold_", ""))

        raise FileNotFoundError(
            f"[ERROR] Modèles manquants:\n"
            f"  - Folds demandés: {VALIDATION_FOLDS}\n"
            f"  - Folds manquants: {missing_folds}\n"
            f"  - Folds disponibles: {available_folds}\n"
            f"Solution: Modifiez VALIDATION_FOLDS pour utiliser seulement les folds disponibles"
        )
    else:
        # Vérifier fold_all
        fold_all_path = os.path.join(trainer_path, "fold_all")
        checkpoint_path = os.path.join(fold_all_path, "checkpoint_final.pth")

        if not os.path.exists(checkpoint_path):
            # Chercher les folds disponibles comme alternative
            available_folds = []
            for item in os.listdir(trainer_path):
                if item.startswith("fold_") and item != "fold_all" and os.path.isdir(os.path.join(trainer_path, item)):
                    checkpoint = os.path.join(trainer_path, item, "checkpoint_final.pth")
                    if os.path.exists(checkpoint):
                        available_folds.append(item.replace("fold_", ""))

            error_msg = (
                f"[ERROR] Modèle 'fold_all' non trouvé: {checkpoint_path}\n"
                f"Le dataset {dataset_name} n'a pas été entraîné en mode 'all'.\n"
            )

            if available_folds:
                error_msg += (
                    f"Solutions disponibles:\n"
                    f"  1. Utilisez la validation croisée:\n"
                    f"     USE_CROSS_VALIDATION = True\n"
                    f"     VALIDATION_FOLDS = {available_folds}\n"
                    f"  2. Entraînez un modèle en mode 'all':\n"
                    f"     Dans train_nnunet_5fold.py: USE_CROSS_VALIDATION = False"
                )
            else:
                error_msg += (
                    f"Aucun modèle trouvé. Vérifiez que l'entraînement s'est terminé correctement."
                )

            raise FileNotFoundError(error_msg)

# Vérifier la disponibilité des modèles
logger.info(f"[CHECK] Vérification des modèles disponibles...")
check_model_availability()
logger.info(f"[CHECK] ✅ Modèles disponibles et compatibles")

# === CONSTRUCTION DE LA COMMANDE ===
# Construire la chaîne des folds pour la commande
folds_str = " ".join(map(str, VALIDATION_FOLDS))

# Construire le suffixe pour le nom de fichier
folds_suffix = f"folds{'_'.join(map(str, VALIDATION_FOLDS))}"

# Affichage informatif selon le contenu de VALIDATION_FOLDS
if len(VALIDATION_FOLDS) == 1 and VALIDATION_FOLDS[0] == "all":
    print(f"[INFO] Mode 'all' - export du modèle entraîné sur toutes les données")
elif "all" in VALIDATION_FOLDS:
    numeric_folds = [f for f in VALIDATION_FOLDS if f != "all"]
    print(f"[INFO] Mode mixte - export des folds individuels: {numeric_folds} + fold 'all'")
else:
    print(f"[INFO] Mode validation croisée - export des folds: {VALIDATION_FOLDS}")

OUTPUT_ZIP = f"model_{DATASET_ID}_{CONFIGURATION}_{folds_suffix}.zip"

cmd = (
    f"nnUNetv2_export_model_to_zip "
    f"-d {dataset_name} "
    f"-o {OUTPUT_ZIP} "
    f"-c {CONFIGURATION} "
    f"-tr {trainer_class} "
    f"-p {PLANS_NAME} "
    f"-f {folds_str}"
)

# === AFFICHAGE ET EXÉCUTION ===
print("Commande générée :")
print(cmd)
print(f"\nFichier de sortie : {OUTPUT_ZIP}")
print("\nExportation en cours...\n")

try:
    subprocess.run(cmd, shell=True, check=True)
    print(f"\nExportation réussie : {OUTPUT_ZIP}")
    
    # Vérifier la taille du fichier
    if os.path.exists(OUTPUT_ZIP):
        file_size = os.path.getsize(OUTPUT_ZIP) / (1024 * 1024)  # MB
        print(f"Taille du fichier : {file_size:.1f} MB")
    
except subprocess.CalledProcessError as e:
    print(f"\nÉchec de l'exportation.")
    print(f"Erreur : {e}")
    print(f"Code de retour : {e.returncode}")

print(f"\nPour installer sur un autre système :")
print(f"nnUNetv2_install_pretrained_model_from_zip {OUTPUT_ZIP}")

import cv2
import os
import argparse
import sys

def create_video_from_images(image_folder, output_video=None, fps=30, extension=".png"):
    """
    Crée une vidéo à partir d'images PNG dans un dossier.

    Args:
        image_folder: Dossier contenant les images
        output_video: Nom du fichier vidéo de sortie (optionnel)
        fps: Images par seconde
        extension: Extension des fichiers image
    """
    # Définir le nom de sortie par défaut si non spécifié
    if output_video is None:
        folder_name = os.path.basename(image_folder.rstrip("\\/"))
        output_video = os.path.join(image_folder, f"{folder_name}_video.mp4")

    print(f"[INFO] Création de vidéo depuis: {image_folder}")
    print(f"[INFO] Fichier de sortie: {output_video}")
    print(f"[INFO] FPS: {fps}, Extension: {extension}")

    # Récupérer et trier les fichiers image
    images = [img for img in os.listdir(image_folder) if img.endswith(extension)]
    images.sort()

    if not images:
        raise ValueError("Aucune image trouvée dans le dossier.")

    # Lire la première image pour connaître la taille
    first_image_path = os.path.join(image_folder, images[0])
    frame = cv2.imread(first_image_path)
    height, width, _ = frame.shape

    # Créer l'objet VideoWriter
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # Codec pour mp4
    video = cv2.VideoWriter(output_video, fourcc, fps, (width, height))

    # Ajouter les images à la vidéo
    for image in images:
        img_path = os.path.join(image_folder, image)
        frame = cv2.imread(img_path)
        video.write(frame)

    video.release()
    print(f"[SUCCESS] Vidéo créée : {output_video}")
    return output_video

def main():
    """Fonction principale avec gestion des arguments"""
    parser = argparse.ArgumentParser(description="Créer une vidéo à partir d'images PNG")
    parser.add_argument(
        "image_folder",
        help="Dossier contenant les images PNG"
    )
    parser.add_argument(
        "--output",
        help="Nom du fichier vidéo de sortie (optionnel, par défaut dans le dossier d'images)"
    )
    parser.add_argument(
        "--fps",
        type=int,
        default=30,
        help="Images par seconde (défaut: 30)"
    )
    parser.add_argument(
        "--extension",
        default=".png",
        help="Extension des fichiers image (défaut: .png)"
    )

    args = parser.parse_args()

    try:
        # Vérifier que le dossier existe
        if not os.path.exists(args.image_folder):
            print(f"[ERROR] Le dossier {args.image_folder} n'existe pas")
            sys.exit(1)

        # Créer la vidéo
        output_video = create_video_from_images(
            image_folder=args.image_folder,
            output_video=args.output,
            fps=args.fps,
            extension=args.extension
        )

        print(f"[SUCCESS] Vidéo créée avec succès: {output_video}")

    except Exception as e:
        print(f"[ERROR] Erreur lors de la création de la vidéo: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

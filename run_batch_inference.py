#!/usr/bin/env python3
"""
Script simple pour lancer infer_nnunet_5fold.py sur tous les sous-dossiers d'un dossier parent.

Usage:
    python run_batch_inference.py
    
Le script va:
1. Parcourir tous les sous-dossiers du dossier parent (défini ci-dessous)
2. Pour chaque sous-dossier contenant des images PNG, lancer infer_nnunet_5fold.py avec --input_folder
3. Passer au sous-dossier suivant
"""

import os
import subprocess
import logging
from pathlib import Path
import re

# === CONFIGURATION ===
# Modifiez ce chemin selon vos besoins
PARENT_FOLDER = r"C:\Users\<USER>\Documents\datasets\Inference_uint8_batch_test"
SCRIPT_PATH = "infer_nnunet_5fold.py"
SKIP_VALIDATION = False  # True pour ignorer la validation des noms de fichiers nnU-Net
CONTINUE_ON_ERROR = True  # True pour continuer même si une inférence échoue

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('batch_inference.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def find_png_files(folder_path):
    """Vérifie si un dossier contient des fichiers PNG"""
    try:
        png_files = [f for f in os.listdir(folder_path) if f.lower().endswith('.png')]
        return len(png_files) > 0, len(png_files)
    except (OSError, PermissionError):
        return False, 0

def validate_nnunet_naming(folder_path):
    """Vérifie si les fichiers PNG respectent le format nnU-Net (XXXX_0000.png)"""
    try:
        png_files = [f for f in os.listdir(folder_path) if f.lower().endswith('.png')]
        if not png_files:
            return False, []
        
        valid_pattern = re.compile(r'^\d{4}_0000\.png$')
        invalid_files = [f for f in png_files if not valid_pattern.match(f)]
        
        return len(invalid_files) == 0, invalid_files
    except (OSError, PermissionError):
        return False, []

def run_inference(subfolder_path):
    """Exécute infer_nnunet_5fold.py pour un sous-dossier donné"""
    try:
        logger.info(f"[START] Début de l'inférence pour: {subfolder_path}")
        
        # Construire la commande (subprocess.run gère automatiquement les espaces)
        cmd = [
            "python",
            SCRIPT_PATH,
            "--input_folder",
            str(subfolder_path)
        ]
        
        logger.info(f"[CMD] Commande: {' '.join(cmd)}")
        
        # Exécuter le script d'inférence sans capturer la sortie pour voir les logs en temps réel
        result = subprocess.run(
            cmd,
            cwd=os.getcwd(),
            timeout=7200  # Timeout de 2 heures par inférence (plus long pour inclure le postprocessing)
        )
        
        if result.returncode == 0:
            logger.info(f"[SUCCESS] Inférence complète réussie pour: {subfolder_path}")
            return True
        else:
            logger.error(f"[ERROR] Erreur lors de l'inférence pour: {subfolder_path}")
            logger.error(f"[ERROR] Code de retour: {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error(f"[TIMEOUT] Timeout lors de l'inférence pour: {subfolder_path}")
        return False
    except Exception as e:
        logger.error(f"[EXCEPTION] Exception lors de l'inférence pour {subfolder_path}: {e}")
        return False

def main():
    # Vérifications initiales
    parent_folder = Path(PARENT_FOLDER)
    script_path = Path(SCRIPT_PATH)
    
    if not parent_folder.exists():
        logger.error(f"Le dossier parent n'existe pas: {parent_folder}")
        return 1
    
    if not script_path.exists():
        logger.error(f"Le script d'inférence n'existe pas: {script_path}")
        return 1
    
    # Trouver tous les sous-dossiers contenant des images PNG
    subfolders_to_process = []
    
    logger.info(f"Recherche des sous-dossiers dans: {parent_folder}")
    
    for item in parent_folder.iterdir():
        if item.is_dir():
            has_png, png_count = find_png_files(item)
            if has_png:
                if not SKIP_VALIDATION:
                    is_valid, invalid_files = validate_nnunet_naming(item)
                    if not is_valid:
                        logger.warning(f"[WARNING] Dossier ignoré (noms non conformes): {item}")
                        logger.warning(f"   Fichiers non conformes: {invalid_files}")
                        continue
                
                subfolders_to_process.append((item, png_count))
                logger.info(f"[FOLDER] Dossier à traiter: {item} ({png_count} fichiers PNG)")
    
    if not subfolders_to_process:
        logger.error("Aucun sous-dossier valide trouvé!")
        return 1
    
    logger.info(f"[START] {len(subfolders_to_process)} dossiers à traiter")
    
    # Traitement de chaque sous-dossier
    successful_inferences = 0
    failed_inferences = 0
    
    for i, (subfolder, png_count) in enumerate(subfolders_to_process, 1):
        logger.info(f"\n{'='*60}")
        logger.info(f"Traitement {i}/{len(subfolders_to_process)}: {subfolder.name}")
        logger.info(f"{'='*60}")
        
        # Exécuter l'inférence
        success = run_inference(subfolder)
        
        if success:
            successful_inferences += 1
        else:
            failed_inferences += 1
            if not CONTINUE_ON_ERROR:
                logger.error("Arrêt du traitement en raison d'une erreur")
                break
    
    # Résumé final
    logger.info(f"\n{'='*60}")
    logger.info(f"RÉSUMÉ FINAL")
    logger.info(f"{'='*60}")
    logger.info(f"[SUCCESS] Inférences réussies: {successful_inferences}")
    logger.info(f"[ERROR] Inférences échouées: {failed_inferences}")
    logger.info(f"[TOTAL] Total traité: {successful_inferences + failed_inferences}/{len(subfolders_to_process)}")
    
    if successful_inferences == len(subfolders_to_process):
        logger.info("[COMPLETE] Toutes les inférences ont réussi!")
        return 0
    else:
        logger.warning(f"[WARNING] {failed_inferences} inférences ont échoué")
        return 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
